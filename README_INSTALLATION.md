# Atlas Business Directory - SQLite Installation

## ✅ Installation erfolgreich abgeschlossen!

Das Atlas Business Directory System wurde erfolgreich für SQLite konfiguriert und ist bereit zur Nutzung.

## 🚀 Server Status

- **Server läuft auf:** http://localhost:8000
- **Admin-Panel:** http://localhost:8000/admin
- **Datenbank:** SQLite (database/atlas.db)

## 👤 Standard-Anmeldedaten

**Admin-Zugang:**
- **Email:** <EMAIL>
- **Passwort:** hello

## 📁 Projektstruktur

```
atlas_2.15/
├── application/          # CodeIgniter Anwendung
├── assets/              # CSS, JS, Bilder
├── database/            # SQLite Datenbank
│   └── atlas.db        # Hauptdatenbank
├── system/              # CodeIgniter System
├── uploads/             # Upload-Verzeichnis
├── index.php           # Haupteinstiegspunkt
└── .htaccess           # URL-Rewriting
```

## 🔧 Konfiguration

### Datenbank-Konfiguration
Die Datenbank wurde automatisch für SQLite konfiguriert:
- **Datei:** `application/config/database.php`
- **Treiber:** sqlite3
- **Pfad:** `database/atlas.db`

### Grundeinstellungen
- **Website-Titel:** Atlas
- **System-Email:** <EMAIL>
- **Währung:** USD
- **Zeitzone:** America/New_York
- **Sprache:** English

## 🌐 Zugriff auf die Anwendung

1. **Frontend:** http://localhost:8000
   - Öffentliche Website für Benutzer
   - Verzeichnis-Listings
   - Suchfunktionen

2. **Admin-Panel:** http://localhost:8000/admin
   - Verwaltung von Listings
   - Benutzer-Management
   - System-Einstellungen

## 📊 Verfügbare Funktionen

### Frontend
- ✅ Business Directory Listings
- ✅ Kategorien-basierte Suche
- ✅ Standort-basierte Suche
- ✅ Benutzer-Registrierung
- ✅ Review-System
- ✅ Responsive Design

### Admin-Panel
- ✅ Dashboard
- ✅ Listings-Verwaltung
- ✅ Kategorien-Verwaltung
- ✅ Benutzer-Verwaltung
- ✅ System-Einstellungen
- ✅ Zahlungs-Konfiguration

## 🛠️ Server-Verwaltung

### Server starten
```bash
cd /Users/<USER>/Downloads/Atlas-business-directory-listing/atlas_2.15
php -S localhost:8000
```

### Server stoppen
Drücken Sie `Ctrl+C` im Terminal

### Datenbank-Backup
```bash
cp database/atlas.db database/atlas_backup_$(date +%Y%m%d_%H%M%S).db
```

## 📝 Nächste Schritte

1. **Admin-Panel besuchen:** http://localhost:8000/admin
2. **Mit Standard-Anmeldedaten einloggen**
3. **System-Einstellungen anpassen:**
   - Website-Titel und -Beschreibung
   - Logo und Favicon
   - E-Mail-Konfiguration
   - Zahlungs-Gateways

4. **Erste Inhalte erstellen:**
   - Kategorien hinzufügen
   - Test-Listings erstellen
   - Benutzer-Rollen konfigurieren

## 🔒 Sicherheitshinweise

- **Passwort ändern:** Ändern Sie das Standard-Admin-Passwort
- **Datenbank-Schutz:** Die .htaccess-Datei schützt die Datenbank vor direktem Zugriff
- **Uploads-Verzeichnis:** Überprüfen Sie die Upload-Berechtigungen

## 🐛 Fehlerbehebung

### Häufige Probleme

1. **Datenbank-Fehler:**
   - Überprüfen Sie, ob `database/atlas.db` existiert
   - Stellen Sie sicher, dass PHP SQLite3-Unterstützung hat

2. **Berechtigungsfehler:**
   ```bash
   chmod 755 database/
   chmod 644 database/atlas.db
   chmod 755 uploads/
   ```

3. **URL-Rewriting funktioniert nicht:**
   - Überprüfen Sie die .htaccess-Datei
   - Stellen Sie sicher, dass mod_rewrite aktiviert ist

## 📞 Support

Bei Problemen überprüfen Sie:
1. PHP-Fehlerlog
2. Browser-Entwicklertools
3. Server-Terminal-Ausgabe

---

**Status:** ✅ Bereit zur Nutzung
**Letzte Aktualisierung:** $(date)

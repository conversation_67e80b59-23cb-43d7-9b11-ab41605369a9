/*!
 * VERSION: beta 1.3.0
 * DATE: 2013-10-21
 * UPDATES AND DOCS AT: http://www.greensock.com
 *
 * @license Copyright (c) 2008-2013, GreenSock. All rights reserved.
 * This work is subject to the terms at http://www.greensock.com/terms_of_use.html or for
 * Club GreenSock members, the software agreement that was issued with your membership.
 * 
 * @author: <PERSON>, <EMAIL>
 **/
(window._gsQueue||(window._gsQueue=[])).push(function(){"use strict";var t=180/Math.PI,e=[],i=[],s=[],r={},n=function(t,e,i,s){this.a=t,this.b=e,this.c=i,this.d=s,this.da=s-t,this.ca=i-t,this.ba=e-t},a=",x,y,z,left,top,right,bottom,marginTop,marginLeft,marginRight,marginBottom,paddingLeft,paddingTop,paddingRight,paddingBottom,backgroundPosition,backgroundPosition_y,",o=function(t,e,i,s){var r={a:t},n={},a={},o={c:s},h=(t+e)/2,l=(e+i)/2,u=(i+s)/2,_=(h+l)/2,c=(l+u)/2,p=(c-_)/8;return r.b=h+(t-h)/4,n.b=_+p,r.c=n.a=(r.b+n.b)/2,n.c=a.a=(_+c)/2,a.b=c-p,o.b=u+(s-u)/4,a.c=o.a=(a.b+o.b)/2,[r,n,a,o]},h=function(t,r,n,a,h){var l,u,_,c,p,f,d,m,g,v,y,T,w,b=t.length-1,x=0,P=t[0].a;for(l=0;b>l;l++)p=t[x],u=p.a,_=p.d,c=t[x+1].d,h?(y=e[l],T=i[l],w=.25*(T+y)*r/(a?.5:s[l]||.5),f=_-(_-u)*(a?.5*r:0!==y?w/y:0),d=_+(c-_)*(a?.5*r:0!==T?w/T:0),m=_-(f+((d-f)*(3*y/(y+T)+.5)/4||0))):(f=_-.5*(_-u)*r,d=_+.5*(c-_)*r,m=_-(f+d)/2),f+=m,d+=m,p.c=g=f,p.b=0!==l?P:P=p.a+.6*(p.c-p.a),p.da=_-u,p.ca=g-u,p.ba=P-u,n?(v=o(u,P,g,_),t.splice(x,1,v[0],v[1],v[2],v[3]),x+=4):x++,P=d;p=t[x],p.b=P,p.c=P+.4*(p.d-P),p.da=p.d-p.a,p.ca=p.c-p.a,p.ba=P-p.a,n&&(v=o(p.a,P,p.c,p.d),t.splice(x,1,v[0],v[1],v[2],v[3]))},l=function(t,s,r,a){var o,h,l,u,_,c,p=[];if(a)for(t=[a].concat(t),h=t.length;--h>-1;)"string"==typeof(c=t[h][s])&&"="===c.charAt(1)&&(t[h][s]=a[s]+Number(c.charAt(0)+c.substr(2)));if(o=t.length-2,0>o)return p[0]=new n(t[0][s],0,0,t[-1>o?0:1][s]),p;for(h=0;o>h;h++)l=t[h][s],u=t[h+1][s],p[h]=new n(l,0,0,u),r&&(_=t[h+2][s],e[h]=(e[h]||0)+(u-l)*(u-l),i[h]=(i[h]||0)+(_-u)*(_-u));return p[h]=new n(t[h][s],0,0,t[h+1][s]),p},u=function(t,n,o,u,_,c){var p,f,d,m,g,v,y,T,w={},b=[],x=c||t[0];_="string"==typeof _?","+_+",":a,null==n&&(n=1);for(f in t[0])b.push(f);if(t.length>1){for(T=t[t.length-1],y=!0,p=b.length;--p>-1;)if(f=b[p],Math.abs(x[f]-T[f])>.05){y=!1;break}y&&(t=t.concat(),c&&t.unshift(c),t.push(t[1]),c=t[t.length-3])}for(e.length=i.length=s.length=0,p=b.length;--p>-1;)f=b[p],r[f]=-1!==_.indexOf(","+f+","),w[f]=l(t,f,r[f],c);for(p=e.length;--p>-1;)e[p]=Math.sqrt(e[p]),i[p]=Math.sqrt(i[p]);if(!u){for(p=b.length;--p>-1;)if(r[f])for(d=w[b[p]],v=d.length-1,m=0;v>m;m++)g=d[m+1].da/i[m]+d[m].da/e[m],s[m]=(s[m]||0)+g*g;for(p=s.length;--p>-1;)s[p]=Math.sqrt(s[p])}for(p=b.length,m=o?4:1;--p>-1;)f=b[p],d=w[f],h(d,n,o,u,r[f]),y&&(d.splice(0,m),d.splice(d.length-m,m));return w},_=function(t,e,i){e=e||"soft";var s,r,a,o,h,l,u,_,c,p,f,d={},m="cubic"===e?3:2,g="soft"===e,v=[];if(g&&i&&(t=[i].concat(t)),null==t||m+1>t.length)throw"invalid Bezier data";for(c in t[0])v.push(c);for(l=v.length;--l>-1;){for(c=v[l],d[c]=h=[],p=0,_=t.length,u=0;_>u;u++)s=null==i?t[u][c]:"string"==typeof(f=t[u][c])&&"="===f.charAt(1)?i[c]+Number(f.charAt(0)+f.substr(2)):Number(f),g&&u>1&&_-1>u&&(h[p++]=(s+h[p-2])/2),h[p++]=s;for(_=p-m+1,p=0,u=0;_>u;u+=m)s=h[u],r=h[u+1],a=h[u+2],o=2===m?0:h[u+3],h[p++]=f=3===m?new n(s,r,a,o):new n(s,(2*r+s)/3,(2*r+a)/3,a);h.length=p}return d},c=function(t,e,i){for(var s,r,n,a,o,h,l,u,_,c,p,f=1/i,d=t.length;--d>-1;)for(c=t[d],n=c.a,a=c.d-n,o=c.c-n,h=c.b-n,s=r=0,u=1;i>=u;u++)l=f*u,_=1-l,s=r-(r=(l*l*a+3*_*(l*o+_*h))*l),p=d*i+u-1,e[p]=(e[p]||0)+s*s},p=function(t,e){e=e>>0||6;var i,s,r,n,a=[],o=[],h=0,l=0,u=e-1,_=[],p=[];for(i in t)c(t[i],a,e);for(r=a.length,s=0;r>s;s++)h+=Math.sqrt(a[s]),n=s%e,p[n]=h,n===u&&(l+=h,n=s/e>>0,_[n]=p,o[n]=l,h=0,p=[]);return{length:l,lengths:o,segments:_}},f=window._gsDefine.plugin({propName:"bezier",priority:-1,API:2,global:!0,init:function(t,e,i){this._target=t,e instanceof Array&&(e={values:e}),this._func={},this._round={},this._props=[],this._timeRes=null==e.timeResolution?6:parseInt(e.timeResolution,10);var s,r,n,a,o,h=e.values||[],l={},c=h[0],f=e.autoRotate||i.vars.orientToBezier;this._autoRotate=f?f instanceof Array?f:[["x","y","rotation",f===!0?0:Number(f)||0]]:null;for(s in c)this._props.push(s);for(n=this._props.length;--n>-1;)s=this._props[n],this._overwriteProps.push(s),r=this._func[s]="function"==typeof t[s],l[s]=r?t[s.indexOf("set")||"function"!=typeof t["get"+s.substr(3)]?s:"get"+s.substr(3)]():parseFloat(t[s]),o||l[s]!==h[0][s]&&(o=l);if(this._beziers="cubic"!==e.type&&"quadratic"!==e.type&&"soft"!==e.type?u(h,isNaN(e.curviness)?1:e.curviness,!1,"thruBasic"===e.type,e.correlate,o):_(h,e.type,l),this._segCount=this._beziers[s].length,this._timeRes){var d=p(this._beziers,this._timeRes);this._length=d.length,this._lengths=d.lengths,this._segments=d.segments,this._l1=this._li=this._s1=this._si=0,this._l2=this._lengths[0],this._curSeg=this._segments[0],this._s2=this._curSeg[0],this._prec=1/this._curSeg.length}if(f=this._autoRotate)for(f[0]instanceof Array||(this._autoRotate=f=[f]),n=f.length;--n>-1;)for(a=0;3>a;a++)s=f[n][a],this._func[s]="function"==typeof t[s]?t[s.indexOf("set")||"function"!=typeof t["get"+s.substr(3)]?s:"get"+s.substr(3)]:!1;return!0},set:function(e){var i,s,r,n,a,o,h,l,u,_,c=this._segCount,p=this._func,f=this._target;if(this._timeRes){if(u=this._lengths,_=this._curSeg,e*=this._length,r=this._li,e>this._l2&&c-1>r){for(l=c-1;l>r&&e>=(this._l2=u[++r]););this._l1=u[r-1],this._li=r,this._curSeg=_=this._segments[r],this._s2=_[this._s1=this._si=0]}else if(this._l1>e&&r>0){for(;r>0&&(this._l1=u[--r])>=e;);0===r&&this._l1>e?this._l1=0:r++,this._l2=u[r],this._li=r,this._curSeg=_=this._segments[r],this._s1=_[(this._si=_.length-1)-1]||0,this._s2=_[this._si]}if(i=r,e-=this._l1,r=this._si,e>this._s2&&_.length-1>r){for(l=_.length-1;l>r&&e>=(this._s2=_[++r]););this._s1=_[r-1],this._si=r}else if(this._s1>e&&r>0){for(;r>0&&(this._s1=_[--r])>=e;);0===r&&this._s1>e?this._s1=0:r++,this._s2=_[r],this._si=r}o=(r+(e-this._s1)/(this._s2-this._s1))*this._prec}else i=0>e?0:e>=1?c-1:c*e>>0,o=(e-i*(1/c))*c;for(s=1-o,r=this._props.length;--r>-1;)n=this._props[r],a=this._beziers[n][i],h=(o*o*a.da+3*s*(o*a.ca+s*a.ba))*o+a.a,this._round[n]&&(h=h+(h>0?.5:-.5)>>0),p[n]?f[n](h):f[n]=h;if(this._autoRotate){var d,m,g,v,y,T,w,b=this._autoRotate;for(r=b.length;--r>-1;)n=b[r][2],T=b[r][3]||0,w=b[r][4]===!0?1:t,a=this._beziers[b[r][0]],d=this._beziers[b[r][1]],a&&d&&(a=a[i],d=d[i],m=a.a+(a.b-a.a)*o,v=a.b+(a.c-a.b)*o,m+=(v-m)*o,v+=(a.c+(a.d-a.c)*o-v)*o,g=d.a+(d.b-d.a)*o,y=d.b+(d.c-d.b)*o,g+=(y-g)*o,y+=(d.c+(d.d-d.c)*o-y)*o,h=Math.atan2(y-g,v-m)*w+T,p[n]?f[n](h):f[n]=h)}}}),d=f.prototype;f.bezierThrough=u,f.cubicToQuadratic=o,f._autoCSS=!0,f.quadraticToCubic=function(t,e,i){return new n(t,(2*e+t)/3,(2*e+i)/3,i)},f._cssRegister=function(){var t=window._gsDefine.globals.CSSPlugin;if(t){var e=t._internals,i=e._parseToProxy,s=e._setPluginRatio,r=e.CSSPropTween;e._registerComplexSpecialProp("bezier",{parser:function(t,e,n,a,o,h){e instanceof Array&&(e={values:e}),h=new f;var l,u,_,c=e.values,p=c.length-1,d=[],m={};if(0>p)return o;for(l=0;p>=l;l++)_=i(t,c[l],a,o,h,p!==l),d[l]=_.end;for(u in e)m[u]=e[u];return m.values=d,o=new r(t,"bezier",0,0,_.pt,2),o.data=_,o.plugin=h,o.setRatio=s,0===m.autoRotate&&(m.autoRotate=!0),!m.autoRotate||m.autoRotate instanceof Array||(l=m.autoRotate===!0?0:Number(m.autoRotate),m.autoRotate=null!=_.end.left?[["left","top","rotation",l,!1]]:null!=_.end.x?[["x","y","rotation",l,!1]]:!1),m.autoRotate&&(a._transform||a._enableTransforms(!1),_.autoRotate=a._target._gsTransform),h._onInitTween(_.proxy,m,a._tween),o}})}},d._roundProps=function(t,e){for(var i=this._overwriteProps,s=i.length;--s>-1;)(t[i[s]]||t.bezier||t.bezierThrough)&&(this._round[i[s]]=e)},d._kill=function(t){var e,i,s=this._props;for(e in this._beziers)if(e in t)for(delete this._beziers[e],delete this._func[e],i=s.length;--i>-1;)s[i]===e&&s.splice(i,1);return this._super._kill.call(this,t)}}),window._gsDefine&&window._gsQueue.pop()();
/*!
 * VERSION: beta 1.2.0
 * DATE: 2013-03-01
 * UPDATES AND DOCS AT: http://www.greensock.com
 *
 * @license Copyright (c) 2008-2013, GreenSock. All rights reserved.
 * This work is subject to the terms at http://www.greensock.com/terms_of_use.html or for
 * Club GreenSock members, the software agreement that was issued with your membership.
 * 
 * @author: <PERSON>, <EMAIL>
 **/
(window._gsQueue||(window._gsQueue=[])).push(function(){"use strict";var t=/(\d|\.)+/g,e={aqua:[0,255,255],lime:[0,255,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,255],navy:[0,0,128],white:[255,255,255],fuchsia:[255,0,255],olive:[128,128,0],yellow:[255,255,0],orange:[255,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[255,0,0],pink:[255,192,203],cyan:[0,255,255],transparent:[255,255,255,0]},i=function(t,e,i){return t=0>t?t+1:t>1?t-1:t,0|255*(1>6*t?e+6*(i-e)*t:.5>t?i:2>3*t?e+6*(i-e)*(2/3-t):e)+.5},s=function(s){if(""===s||null==s||"none"===s)return e.transparent;if(e[s])return e[s];if("number"==typeof s)return[s>>16,255&s>>8,255&s];if("#"===s.charAt(0))return 4===s.length&&(s="#"+s.charAt(1)+s.charAt(1)+s.charAt(2)+s.charAt(2)+s.charAt(3)+s.charAt(3)),s=parseInt(s.substr(1),16),[s>>16,255&s>>8,255&s];if("hsl"===s.substr(0,3)){s=s.match(t);var r=Number(s[0])%360/360,n=Number(s[1])/100,a=Number(s[2])/100,o=.5>=a?a*(n+1):a+n-a*n,h=2*a-o;return s.length>3&&(s[3]=Number(s[3])),s[0]=i(r+1/3,h,o),s[1]=i(r,h,o),s[2]=i(r-1/3,h,o),s}return s.match(t)||e.transparent};window._gsDefine.plugin({propName:"colorProps",priority:-1,API:2,init:function(t,e){this._target=t;var i,r,n,a;for(i in e)n=s(e[i]),this._firstPT=a={_next:this._firstPT,p:i,f:"function"==typeof t[i],n:i,r:!1},r=s(a.f?t[i.indexOf("set")||"function"!=typeof t["get"+i.substr(3)]?i:"get"+i.substr(3)]():t[i]),a.s=Number(r[0]),a.c=Number(n[0])-a.s,a.gs=Number(r[1]),a.gc=Number(n[1])-a.gs,a.bs=Number(r[2]),a.bc=Number(n[2])-a.bs,(a.rgba=r.length>3||n.length>3)&&(a.as=4>r.length?1:Number(r[3]),a.ac=(4>n.length?1:Number(n[3]))-a.as),a._next&&(a._next._prev=a);return!0},set:function(t){for(var e,i=this._firstPT;i;)e=(i.rgba?"rgba(":"rgb(")+(i.s+t*i.c>>0)+", "+(i.gs+t*i.gc>>0)+", "+(i.bs+t*i.bc>>0)+(i.rgba?", "+(i.as+t*i.ac):"")+")",i.f?this._target[i.p](e):this._target[i.p]=e,i=i._next}})}),window._gsDefine&&window._gsQueue.pop()();
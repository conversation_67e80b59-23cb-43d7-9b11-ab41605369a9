/*!
 * VERSION: beta 0.1.5
 * DATE: 2013-08-29
 * UPDATES AND DOCS AT: http://www.greensock.com
 *
 * @license Copyright (c) 2008-2013, GreenSock. All rights reserved.
 * This work is subject to the terms at http://www.greensock.com/terms_of_use.html or for
 * Club GreenSock members, the software agreement that was issued with your membership.
 * 
 * @author: <PERSON>, <EMAIL>
 **/
(window._gsQueue||(window._gsQueue=[])).push(function(){"use strict";var t,e,i=/(\d|\.)+/g,s=["redMultiplier","greenMultiplier","blueMultiplier","alphaMultiplier","redOffset","greenOffset","blueOffset","alphaOffset"],r={aqua:[0,255,255],lime:[0,255,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,255],navy:[0,0,128],white:[255,255,255],fuchsia:[255,0,255],olive:[128,128,0],yellow:[255,255,0],orange:[255,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[255,0,0],pink:[255,192,203],cyan:[0,255,255],transparent:[255,255,255,0]},n=function(t){return""===t||null==t||"none"===t?r.transparent:r[t]?r[t]:"number"==typeof t?[t>>16,255&t>>8,255&t]:"#"===t.charAt(0)?(4===t.length&&(t="#"+t.charAt(1)+t.charAt(1)+t.charAt(2)+t.charAt(2)+t.charAt(3)+t.charAt(3)),t=parseInt(t.substr(1),16),[t>>16,255&t>>8,255&t]):t.match(i)||r.transparent},a=function(e,i,r){if(!t&&(t=window.ColorFilter||window.createjs.ColorFilter,!t))throw"EaselPlugin error: The EaselJS ColorFilter JavaScript file wasn't loaded.";for(var a,o,l,h,u,_=e.filters||[],p=_.length;--p>-1;)if(_[p]instanceof t){o=_[p];break}if(o||(o=new t,_.push(o),e.filters=_),l=o.clone(),null!=i.tint)a=n(i.tint),h=null!=i.tintAmount?Number(i.tintAmount):1,l.redOffset=Number(a[0])*h,l.greenOffset=Number(a[1])*h,l.blueOffset=Number(a[2])*h,l.redMultiplier=l.greenMultiplier=l.blueMultiplier=1-h;else for(u in i)"exposure"!==u&&"brightness"!==u&&(l[u]=Number(i[u]));for(null!=i.exposure?(l.redOffset=l.greenOffset=l.blueOffset=255*(Number(i.exposure)-1),l.redMultiplier=l.greenMultiplier=l.blueMultiplier=1):null!=i.brightness&&(h=Number(i.brightness)-1,l.redOffset=l.greenOffset=l.blueOffset=h>0?255*h:0,l.redMultiplier=l.greenMultiplier=l.blueMultiplier=1-Math.abs(h)),p=8;--p>-1;)u=s[p],o[u]!==l[u]&&r._addTween(o,u,o[u],l[u],"easel_colorFilter");if(r._overwriteProps.push("easel_colorFilter"),!e.cacheID)throw"EaselPlugin warning: for filters to display in EaselJS, you must call the object's cache() method first. "+e},o=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],l=.212671,h=.71516,u=.072169,_=function(t,e){if(!(t instanceof Array&&e instanceof Array))return e;var i,s,r=[],n=0,a=0;for(i=0;4>i;i++){for(s=0;5>s;s++)a=4===s?t[n+4]:0,r[n+s]=t[n]*e[s]+t[n+1]*e[s+5]+t[n+2]*e[s+10]+t[n+3]*e[s+15]+a;n+=5}return r},p=function(t,e){if(isNaN(e))return t;var i=1-e,s=i*l,r=i*h,n=i*u;return _([s+e,r,n,0,0,s,r+e,n,0,0,s,r,n+e,0,0,0,0,0,1,0],t)},f=function(t,e,i){isNaN(i)&&(i=1);var s=n(e),r=s[0]/255,a=s[1]/255,o=s[2]/255,p=1-i;return _([p+i*r*l,i*r*h,i*r*u,0,0,i*a*l,p+i*a*h,i*a*u,0,0,i*o*l,i*o*h,p+i*o*u,0,0,0,0,0,1,0],t)},c=function(t,e){if(isNaN(e))return t;e*=Math.PI/180;var i=Math.cos(e),s=Math.sin(e);return _([l+i*(1-l)+s*-l,h+i*-h+s*-h,u+i*-u+s*(1-u),0,0,l+i*-l+.143*s,h+i*(1-h)+.14*s,u+i*-u+s*-.283,0,0,l+i*-l+s*-(1-l),h+i*-h+s*h,u+i*(1-u)+s*u,0,0,0,0,0,1,0,0,0,0,0,1],t)},d=function(t,e){return isNaN(e)?t:(e+=.01,_([e,0,0,0,128*(1-e),0,e,0,0,128*(1-e),0,0,e,0,128*(1-e),0,0,0,1,0],t))},m=function(t,i,s){if(!e&&(e=window.ColorMatrixFilter||window.createjs.ColorMatrixFilter,!e))throw"EaselPlugin error: The EaselJS ColorMatrixFilter JavaScript file wasn't loaded.";for(var r,n,a,l=t.filters||[],h=l.length;--h>-1;)if(l[h]instanceof e){a=l[h];break}for(a||(a=new e(o.slice()),l.push(a),t.filters=l),n=a.matrix,r=o.slice(),null!=i.colorize&&(r=f(r,i.colorize,Number(i.colorizeAmount))),null!=i.contrast&&(r=d(r,Number(i.contrast))),null!=i.hue&&(r=c(r,Number(i.hue))),null!=i.saturation&&(r=p(r,Number(i.saturation))),h=r.length;--h>-1;)r[h]!==n[h]&&s._addTween(n,h,n[h],r[h],"easel_colorMatrixFilter");if(s._overwriteProps.push("easel_colorMatrixFilter"),!t.cacheID)throw"EaselPlugin warning: for filters to display in EaselJS, you must call the object's cache() method first. "+t;s._matrix=n};window._gsDefine.plugin({propName:"easel",priority:-1,API:2,init:function(t,e){this._target=t;var i,s,r,n;for(i in e)"colorFilter"===i||"tint"===i||"tintAmount"===i||"exposure"===i||"brightness"===i?r||(a(t,e.colorFilter||e,this),r=!0):"saturation"===i||"contrast"===i||"hue"===i||"colorize"===i||"colorizeAmount"===i?n||(m(t,e.colorMatrixFilter||e,this),n=!0):"frame"===i?(this._firstPT=s={_next:this._firstPT,t:t,p:"gotoAndStop",s:t.currentFrame,f:!0,n:"frame",pr:0,type:0,r:!0},s.c="number"==typeof e[i]?e[i]-s.s:"string"==typeof e[i]?parseFloat(e[i].split("=").join("")):0,s._next&&(s._next._prev=s)):null!=t[i]&&(this._firstPT=s={_next:this._firstPT,t:t,p:i,f:"function"==typeof t[i],n:i,pr:0,type:0},s.s=s.f?t[i.indexOf("set")||"function"!=typeof t["get"+i.substr(3)]?i:"get"+i.substr(3)]():parseFloat(t[i]),s.c="number"==typeof e[i]?e[i]-s.s:"string"==typeof e[i]?parseFloat(e[i].split("=").join("")):0,s._next&&(s._next._prev=s));return!0},set:function(t){for(var e,i=this._firstPT,s=1e-6;i;)e=i.c*t+i.s,i.r?e=e+(e>0?.5:-.5)>>0:s>e&&e>-s&&(e=0),i.f?i.t[i.p](e):i.t[i.p]=e,i=i._next;this._target.cacheID&&this._target.updateCache()}})}),window._gsDefine&&window._gsQueue.pop()();
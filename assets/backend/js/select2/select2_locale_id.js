/**
 * Select2 Indonesian translation.
 * 
 * Author: <PERSON> <i<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>
 */
(function ($) {
    "use strict";

    $.extend($.fn.select2.defaults, {
        formatNoMatches: function () { return "Tidak ada data yang sesuai"; },
        formatInputTooShort: function (input, min) { var n = min - input.length; return "Masukkan " + n + " huruf lagi" + (n == 1 ? "" : "s"); },
        formatInputTooLong: function (input, max) { var n = input.length - max; return "Hapus " + n + " huruf" + (n == 1 ? "" : "s"); },
        formatSelectionTooBig: function (limit) { return "Anda hanya dapat memilih " + limit + " pilihan" + (limit == 1 ? "" : "s"); },
        formatLoadMore: function (pageNumber) { return "Mengambil data..."; },
        formatSearching: function () { return "Mencari..."; }
    });
})(jQuery);

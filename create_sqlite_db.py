#!/usr/bin/env python3
"""
Create SQLite database with basic structure for Atlas
"""
import sqlite3
import os

def create_sqlite_database():
    # Remove existing database if it exists
    db_path = 'database/atlas.db'
    if os.path.exists(db_path):
        os.remove(db_path)
    
    # Create connection to SQLite database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create basic tables needed for Atlas to work
    
    # Users table
    cursor.execute('''
        CREATE TABLE user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT DEFAULT '',
            email TEXT DEFAULT '',
            address TEXT DEFAULT NULL,
            phone TEXT DEFAULT '',
            website TEXT DEFAULT '',
            social TEXT DEFAULT NULL,
            about TEXT DEFAULT NULL,
            password TEXT DEFAULT '',
            role_id INTEGER DEFAULT NULL,
            wishlists TEXT DEFAULT NULL,
            verification_code TEXT DEFAULT NULL,
            is_verified INTEGER NOT NULL DEFAULT 0,
            updated_date TEXT NOT NULL DEFAULT '',
            meta_pixel TEXT DEFAULT NULL
        )
    ''')
    
    # Role table
    cursor.execute('''
        CREATE TABLE role (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT DEFAULT NULL
        )
    ''')
    
    # Settings table
    cursor.execute('''
        CREATE TABLE settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT NOT NULL DEFAULT '',
            description TEXT DEFAULT NULL
        )
    ''')
    
    # Category table
    cursor.execute('''
        CREATE TABLE category (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            parent INTEGER DEFAULT 0,
            icon_class TEXT DEFAULT NULL,
            name TEXT DEFAULT NULL,
            slug TEXT DEFAULT NULL,
            thumbnail TEXT DEFAULT NULL
        )
    ''')
    
    # Country table
    cursor.execute('''
        CREATE TABLE country (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT DEFAULT NULL,
            code TEXT DEFAULT NULL,
            dial_code TEXT DEFAULT NULL,
            currency_name TEXT DEFAULT NULL,
            currency_symbol TEXT DEFAULT NULL,
            currency_code TEXT DEFAULT NULL
        )
    ''')
    
    # State table
    cursor.execute('''
        CREATE TABLE state (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT DEFAULT NULL,
            slug TEXT DEFAULT NULL,
            country_id INTEGER DEFAULT NULL
        )
    ''')
    
    # City table
    cursor.execute('''
        CREATE TABLE city (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT DEFAULT NULL,
            slug TEXT DEFAULT NULL,
            country_id INTEGER DEFAULT NULL,
            state_id INTEGER DEFAULT NULL
        )
    ''')
    
    # Listing table
    cursor.execute('''
        CREATE TABLE listing (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT DEFAULT NULL,
            name TEXT DEFAULT NULL,
            description TEXT DEFAULT NULL,
            categories TEXT DEFAULT NULL,
            amenities TEXT DEFAULT NULL,
            photos TEXT DEFAULT NULL,
            video_url TEXT DEFAULT NULL,
            video_provider TEXT DEFAULT NULL,
            tags TEXT DEFAULT NULL,
            address TEXT DEFAULT NULL,
            email TEXT DEFAULT NULL,
            phone TEXT DEFAULT NULL,
            website TEXT DEFAULT NULL,
            social TEXT DEFAULT NULL,
            user_id INTEGER DEFAULT NULL,
            latitude TEXT DEFAULT NULL,
            longitude TEXT DEFAULT NULL,
            country_id INTEGER DEFAULT NULL,
            city_id INTEGER DEFAULT NULL,
            status TEXT NOT NULL DEFAULT 'pending',
            listing_type TEXT DEFAULT NULL,
            listing_thumbnail TEXT DEFAULT NULL,
            listing_cover TEXT DEFAULT NULL,
            seo_meta_tags TEXT DEFAULT NULL,
            meta_description TEXT DEFAULT NULL,
            date_added TEXT DEFAULT NULL,
            date_modified INTEGER DEFAULT NULL,
            is_featured INTEGER NOT NULL DEFAULT 0,
            google_analytics_id TEXT DEFAULT NULL,
            package_expiry_date TEXT DEFAULT NULL,
            state_id INTEGER DEFAULT NULL,
            price_range TEXT DEFAULT 'no',
            opened_minutes TEXT DEFAULT 'no',
            closed_minutes TEXT DEFAULT 'no'
        )
    ''')
    
    # CI Sessions table
    cursor.execute('''
        CREATE TABLE ci_sessions (
            id TEXT PRIMARY KEY,
            ip_address TEXT NOT NULL,
            timestamp INTEGER NOT NULL DEFAULT 0,
            data BLOB DEFAULT NULL
        )
    ''')
    
    # Insert default roles
    cursor.execute("INSERT INTO role (id, name) VALUES (1, 'Admin')")
    cursor.execute("INSERT INTO role (id, name) VALUES (2, 'User')")
    
    # Add more required tables

    # Frontend settings table
    cursor.execute('''
        CREATE TABLE frontend_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT DEFAULT NULL,
            description TEXT DEFAULT NULL
        )
    ''')

    # Currency table
    cursor.execute('''
        CREATE TABLE currency (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT DEFAULT NULL,
            code TEXT DEFAULT NULL,
            symbol TEXT DEFAULT NULL,
            paypal_supported INTEGER NOT NULL DEFAULT 0,
            stripe_supported INTEGER NOT NULL DEFAULT 0
        )
    ''')

    # Package table
    cursor.execute('''
        CREATE TABLE package (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT DEFAULT NULL,
            price INTEGER DEFAULT NULL,
            validity INTEGER NOT NULL DEFAULT 0,
            ability_to_add_video INTEGER NOT NULL DEFAULT 0,
            ability_to_add_contact_form INTEGER NOT NULL DEFAULT 0,
            number_of_photos INTEGER NOT NULL DEFAULT 0,
            number_of_tags INTEGER NOT NULL DEFAULT 0,
            number_of_categories INTEGER NOT NULL DEFAULT 0,
            is_recommended INTEGER NOT NULL DEFAULT 0,
            package_type INTEGER NOT NULL DEFAULT 0,
            number_of_listings INTEGER NOT NULL DEFAULT 0,
            featured INTEGER NOT NULL DEFAULT 0
        )
    ''')

    # Insert default admin user (password: admin123)
    cursor.execute('''
        INSERT INTO user (id, name, email, password, role_id, is_verified, updated_date)
        VALUES (1, 'Admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 1, datetime('now'))
    ''')
    
    # Insert basic settings
    settings_data = [
        (1, 'website_title', 'Atlas'),
        (2, 'system_title', 'ATLAS Directory Listing CMS'),
        (4, 'system_email', '<EMAIL>'),
        (5, 'address', 'New York'),
        (6, 'phone', '1234567890'),
        (8, 'country_id', '230'),  # United States
        (10, 'currency_position', 'left'),
        (11, 'language', 'english'),
        (12, 'purchase_code', 'your-purchase-code'),
        (13, 'timezone', 'America/New_York'),
        (18, 'system_currency', 'USD'),
        (37, 'version', '2.15'),
        (38, 'meta_keyword', 'business'),
        (39, 'meta_description', 'Atlas business directory listing'),
        (44, 'default_location', '40.702210, -74.015880'),
        (45, 'active_map', 'openstreetmap')
    ]
    
    cursor.executemany("INSERT INTO settings (id, type, description) VALUES (?, ?, ?)", settings_data)
    
    # Insert United States as default country
    cursor.execute('''
        INSERT INTO country (id, name, code, dial_code, currency_name, currency_symbol, currency_code)
        VALUES (230, 'United States', 'US', '+1', 'United States dollar', '$', 'USD')
    ''')

    # Insert default currency
    cursor.execute('''
        INSERT INTO currency (id, name, code, symbol, paypal_supported, stripe_supported)
        VALUES (2, 'Dollars', 'USD', '$', 1, 1)
    ''')

    # Insert frontend settings
    frontend_settings_data = [
        (1, 'banner_title', 'Atlas Business Directory Listing'),
        (2, 'banner_sub_title', 'Subtitle Of Atlas Directory Listing'),
        (3, 'about_us', '<p>About us</p>'),
        (4, 'terms_and_condition', '<p>Terms and conditions</p>'),
        (5, 'privacy_policy', '<p>Privacy Policy</p>'),
        (6, 'social_links', '{"facebook":"","twitter":"","linkedin":"","google":"","instagram":"","pinterest":""}'),
        (7, 'slogan', 'Find your local places, you love most to roam around.'),
        (8, 'faq', '<p>FAQ</p>'),
        (9, 'cookie_note', 'This Website Uses Cookies To Personalize Content And Analyse Traffic In Order To Offer You A Better Experience.'),
        (10, 'cookie_status', '1'),
        (14, 'home_page', '1')
    ]

    cursor.executemany("INSERT INTO frontend_settings (id, type, description) VALUES (?, ?, ?)", frontend_settings_data)
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print("SQLite database created successfully: database/atlas.db")
    print("Default admin user: <EMAIL> / admin123")

if __name__ == '__main__':
    create_sqlite_database()

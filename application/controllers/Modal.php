<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 *  <AUTHOR> Creativeitem
 *  date    : 14 september, 2017
 *  ATLAS Directory Listing
 *  http://codecanyon.net/user/Creativeitem
 *  http://support.creativeitem.com
 */
class Modal extends CI_Controller {


	function __construct()
  {
    parent::__construct();
    $this->load->database();
    $this->load->library('session');
    /*cache control*/
    $this->output->set_header('Last-Modified: ' . gmdate("D, d M Y H:i:s") . ' GMT');
    $this->output->set_header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
    $this->output->set_header('Pragma: no-cache');
    $this->output->set_header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
		// Set the timezone
		date_default_timezone_set(get_settings('timezone'));
  }

	function popup($page_name = '' , $param2 = '' , $param3 = '')
	{
   
		$logged_in_user_role 		= strtolower($this->session->userdata('role'));
		$page_data['param2']		=	$param2;
		$page_data['param3']		=	$param3;
		$this->load->view( 'backend/'.$logged_in_user_role.'/'.$page_name.'.php' ,$page_data);
	}
}

<?php
/**
 * PHPMailer Exception class.
 * PHP Version 5.5.
 *
 * @see       https://github.com/PHPMailer/PHPMailer/ The PHPMailer GitHub project
 *
 * <AUTHOR> (Synchro/coolbru) <<EMAIL>>
 * <AUTHOR> (jimjag) <<EMAIL>>
 * <AUTHOR> (codeworxtech) <<EMAIL>>
 * <AUTHOR> (original founder)
 * @copyright 2012 - 2017 <PERSON>
 * @copyright 2010 - 2012 <PERSON>
 * @copyright 2004 - 2009 <PERSON>
 * @license   http://www.gnu.org/copyleft/lesser.html GNU Lesser General Public License
 * @note      This program is distributed in the hope that it will be useful - WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.
 */

namespace PHPMailer\PHPMailer;

/**
 * PHPMailer exception handler.
 *
 * <AUTHOR> <<EMAIL>>
 */
class Exception extends \Exception
{
    /**
     * Prettify error message output.
     *
     * @return string
     */
    public function errorMessage()
    {
        return '<strong>' . htmlspecialchars($this->getMessage()) . "</strong><br />\n";
    }
}

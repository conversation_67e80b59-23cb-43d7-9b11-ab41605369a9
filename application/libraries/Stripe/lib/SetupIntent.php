<?php

namespace Stripe;

/**
 * A SetupIntent guides you through the process of setting up and saving a
 * customer's payment credentials for future payments. For example, you could use a
 * SetupIntent to set up and save your customer's card without immediately
 * collecting a payment. Later, you can use <a
 * href="https://stripe.com/docs/api#payment_intents">PaymentIntents</a> to drive
 * the payment flow.
 *
 * Create a SetupIntent as soon as you're ready to collect your customer's payment
 * credentials. Do not maintain long-lived, unconfirmed SetupIntents as they may no
 * longer be valid. The SetupIntent then transitions through multiple <a
 * href="https://stripe.com/docs/payments/intents#intent-statuses">statuses</a> as
 * it guides you through the setup process.
 *
 * Successful SetupIntents result in payment credentials that are optimized for
 * future payments. For example, cardholders in <a
 * href="/guides/strong-customer-authentication">certain regions</a> may need to be
 * run through <a
 * href="https://stripe.com/docs/strong-customer-authentication">Strong Customer
 * Authentication</a> at the time of payment method collection in order to
 * streamline later <a
 * href="https://stripe.com/docs/payments/setup-intents">off-session payments</a>.
 * If the SetupIntent is used with a <a
 * href="https://stripe.com/docs/api#setup_intent_object-customer">Customer</a>,
 * upon success, it will automatically attach the resulting payment method to that
 * Customer. We recommend using SetupIntents or <a
 * href="https://stripe.com/docs/api#payment_intent_object-setup_future_usage">setup_future_usage</a>
 * on PaymentIntents to save payment methods in order to prevent saving invalid or
 * unoptimized payment methods.
 *
 * By using SetupIntents, you ensure that your customers experience the minimum set
 * of required friction, even as regulations change over time.
 *
 * Related guide: <a href="https://stripe.com/docs/payments/setup-intents">Setup
 * Intents API</a>.
 *
 * @property string $id Unique identifier for the object.
 * @property string $object String representing the object's type. Objects of the same type share the same value.
 * @property null|string|\Stripe\StripeObject $application ID of the Connect application that created the SetupIntent.
 * @property null|string $cancellation_reason Reason for cancellation of this SetupIntent, one of <code>abandoned</code>, <code>requested_by_customer</code>, or <code>duplicate</code>.
 * @property null|string $client_secret <p>The client secret of this SetupIntent. Used for client-side retrieval using a publishable key.</p><p>The client secret can be used to complete payment setup from your frontend. It should not be stored, logged, embedded in URLs, or exposed to anyone other than the customer. Make sure that you have TLS enabled on any page that includes the client secret.</p>
 * @property int $created Time at which the object was created. Measured in seconds since the Unix epoch.
 * @property null|string|\Stripe\Customer $customer <p>ID of the Customer this SetupIntent belongs to, if one exists.</p><p>If present, the SetupIntent's payment method will be attached to the Customer on successful setup. Payment methods attached to other Customers cannot be used with this SetupIntent.</p>
 * @property null|string $description An arbitrary string attached to the object. Often useful for displaying to users.
 * @property null|\Stripe\ErrorObject $last_setup_error The error encountered in the previous SetupIntent confirmation.
 * @property bool $livemode Has the value <code>true</code> if the object exists in live mode or the value <code>false</code> if the object exists in test mode.
 * @property null|string|\Stripe\Mandate $mandate ID of the multi use Mandate generated by the SetupIntent.
 * @property \Stripe\StripeObject $metadata Set of key-value pairs that you can attach to an object. This can be useful for storing additional information about the object in a structured format.
 * @property null|\Stripe\StripeObject $next_action If present, this property tells you what actions you need to take in order for your customer to continue payment setup.
 * @property null|string|\Stripe\Account $on_behalf_of The account (if any) for which the setup is intended.
 * @property null|string|\Stripe\PaymentMethod $payment_method ID of the payment method used with this SetupIntent.
 * @property null|\Stripe\StripeObject $payment_method_options Payment-method-specific configuration for this SetupIntent.
 * @property string[] $payment_method_types The list of payment method types (e.g. card) that this SetupIntent is allowed to set up.
 * @property null|string|\Stripe\Mandate $single_use_mandate ID of the single_use Mandate generated by the SetupIntent.
 * @property string $status <a href="https://stripe.com/docs/payments/intents#intent-statuses">Status</a> of this SetupIntent, one of <code>requires_payment_method</code>, <code>requires_confirmation</code>, <code>requires_action</code>, <code>processing</code>, <code>canceled</code>, or <code>succeeded</code>.
 * @property string $usage <p>Indicates how the payment method is intended to be used in the future.</p><p>Use <code>on_session</code> if you intend to only reuse the payment method when the customer is in your checkout flow. Use <code>off_session</code> if your customer may or may not be in your checkout flow. If not provided, this value defaults to <code>off_session</code>.</p>
 */
class SetupIntent extends ApiResource
{
    const OBJECT_NAME = 'setup_intent';

    use ApiOperations\All;
    use ApiOperations\Create;
    use ApiOperations\Retrieve;
    use ApiOperations\Update;

    const STATUS_CANCELED = 'canceled';
    const STATUS_PROCESSING = 'processing';
    const STATUS_REQUIRES_ACTION = 'requires_action';
    const STATUS_REQUIRES_CONFIRMATION = 'requires_confirmation';
    const STATUS_REQUIRES_PAYMENT_METHOD = 'requires_payment_method';
    const STATUS_SUCCEEDED = 'succeeded';

    /**
     * @param null|array $params
     * @param null|array|string $opts
     *
     * @throws \Stripe\Exception\ApiErrorException if the request fails
     *
     * @return SetupIntent the canceled setup intent
     */
    public function cancel($params = null, $opts = null)
    {
        $url = $this->instanceUrl() . '/cancel';
        list($response, $opts) = $this->_request('post', $url, $params, $opts);
        $this->refreshFrom($response, $opts);

        return $this;
    }

    /**
     * @param null|array $params
     * @param null|array|string $opts
     *
     * @throws \Stripe\Exception\ApiErrorException if the request fails
     *
     * @return SetupIntent the confirmed setup intent
     */
    public function confirm($params = null, $opts = null)
    {
        $url = $this->instanceUrl() . '/confirm';
        list($response, $opts) = $this->_request('post', $url, $params, $opts);
        $this->refreshFrom($response, $opts);

        return $this;
    }
}

#!/usr/bin/env python3
"""
Convert MySQL SQL dump to SQLite compatible format
"""
import re
import sys

def convert_mysql_to_sqlite(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # Remove MySQL specific comments and settings
    content = re.sub(r'--.*?\n', '\n', content)
    content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
    content = re.sub(r'SET.*?;', '', content, flags=re.IGNORECASE)
    content = re.sub(r'START TRANSACTION;', '', content, flags=re.IGNORECASE)
    content = re.sub(r'COMMIT;', '', content, flags=re.IGNORECASE)

    # Remove backticks first
    content = re.sub(r'`', '', content)

    # Convert MySQL data types to SQLite
    # INT variations
    content = re.sub(r'int\(\d+\)', 'INTEGER', content, flags=re.IGNORECASE)
    content = re.sub(r'tinyint\(\d+\)', 'INTEGER', content, flags=re.IGNORECASE)
    content = re.sub(r'smallint\(\d+\)', 'INTEGER', content, flags=re.IGNORECASE)
    content = re.sub(r'mediumint\(\d+\)', 'INTEGER', content, flags=re.IGNORECASE)
    content = re.sub(r'bigint\(\d+\)', 'INTEGER', content, flags=re.IGNORECASE)

    # VARCHAR and TEXT variations
    content = re.sub(r'varchar\(\d+\)', 'TEXT', content, flags=re.IGNORECASE)
    content = re.sub(r'longtext', 'TEXT', content, flags=re.IGNORECASE)
    content = re.sub(r'mediumtext', 'TEXT', content, flags=re.IGNORECASE)
    content = re.sub(r'\btext\b', 'TEXT', content, flags=re.IGNORECASE)

    # BLOB
    content = re.sub(r'blob', 'BLOB', content, flags=re.IGNORECASE)

    # FLOAT and DOUBLE
    content = re.sub(r'float', 'REAL', content, flags=re.IGNORECASE)
    content = re.sub(r'double', 'REAL', content, flags=re.IGNORECASE)

    # Remove MySQL specific table options
    content = re.sub(r'ENGINE=\w+\s*', '', content, flags=re.IGNORECASE)
    content = re.sub(r'DEFAULT CHARSET=\w+\s*', '', content, flags=re.IGNORECASE)
    content = re.sub(r'COLLATE=\w+\s*', '', content, flags=re.IGNORECASE)
    content = re.sub(r'AUTO_INCREMENT=\d+\s*', '', content, flags=re.IGNORECASE)

    # Convert AUTO_INCREMENT to AUTOINCREMENT
    content = re.sub(r'AUTO_INCREMENT', 'AUTOINCREMENT', content, flags=re.IGNORECASE)

    # Remove UNSIGNED
    content = re.sub(r'UNSIGNED\s*', '', content, flags=re.IGNORECASE)

    # Remove COMMENT clauses
    content = re.sub(r'COMMENT\s+\'[^\']*\'', '', content, flags=re.IGNORECASE)
    content = re.sub(r'COMMENT\s+"[^"]*"', '', content, flags=re.IGNORECASE)

    # Split into statements properly
    statements = []
    current_statement = ""
    in_string = False
    escape_next = False

    for char in content:
        if escape_next:
            current_statement += char
            escape_next = False
            continue

        if char == '\\':
            escape_next = True
            current_statement += char
            continue

        if char in ["'", '"'] and not escape_next:
            in_string = not in_string

        current_statement += char

        if char == ';' and not in_string:
            statements.append(current_statement.strip())
            current_statement = ""

    # Clean and filter statements
    cleaned_statements = []
    for statement in statements:
        statement = statement.strip()
        if (statement and
            not statement.startswith('--') and
            not statement.upper().startswith('SET') and
            not statement.upper().startswith('START') and
            not statement.upper().startswith('COMMIT') and
            statement != ';'):

            # Clean up extra whitespace
            statement = re.sub(r'\s+', ' ', statement)

            # Remove trailing commas before closing parenthesis
            statement = re.sub(r',(\s*\))', r'\1', statement)

            # Remove semicolon at the end for processing
            if statement.endswith(';'):
                statement = statement[:-1]

            cleaned_statements.append(statement)

    # Write to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        for statement in cleaned_statements:
            if statement.strip():
                f.write(statement + ';\n\n')

if __name__ == '__main__':
    convert_mysql_to_sqlite('assets/install.sql', 'database/atlas_sqlite.sql')
    print("Conversion completed: database/atlas_sqlite.sql")
